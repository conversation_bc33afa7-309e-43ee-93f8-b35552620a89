#!/bin/bash

# install homebrew
command -v brew >/dev/null 2>&1 || { echo >&2 "Installing Homebrew Now"; \
/usr/bin/ruby -e "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install)"; }

# install pyenv
command -v pyenv >/dev/null 2>&1 || { echo >&2 "Installing pyenv"; brew install pyenv; }

# install python version
if command -v pyenv >/dev/null 2>&1; then
  PYTHON_VERSION=$(cat .python-version)
  if ! pyenv versions --bare | grep -q "^$PYTHON_VERSION$"; then
    echo "Installing Python $PYTHON_VERSION"
    pyenv install "$PYTHON_VERSION"
  else
    echo "Python $PYTHON_VERSION is already installed"
  fi
fi


# install setup tools for python
brew install python-setuptools pkg-config cairo libpng jpeg giflib pango
pip install setuptools
# install ollama
# command -v ollama >/dev/null 2>&1 || { echo >&2 "Installing Ollama"; \
# brew install ollama; }


# serve ollama
# if you want to run ollama, use --ollama flag
if [[ "$*" == *"--ollama"* ]]; then
  echo "Starting Ollama server and running mistral-nemo model"
  ollama serve & ollama run mistral-nemo 2>&1 &
else
  echo "Skipping Ollama server (use --ollama flag to start it)"
fi

# install nvm
if [ -d "${HOME}/.nvm/.git" ]; then 
    echo "nvm installed"; 
else 
    echo "nvm not installed, installing nvm"; 
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash
fi
# source nvm to get nvm command
source ~/.nvm/nvm.sh
# install current node version
nvm install
npm install -g pnpm
npm install -g node-gyp

export NODE_OPTIONS="--max-old-space-size=24576"

echo "using flowise as application, selecting correct package.json"
cd ./electron-flowise

# clean up past builds
rm -rf node_modules
rm ./package-lock.json

# build and set up flowise
cd ./Flowise
# pull from correct branch (change me when ready to upgrade to specific version)
git checkout 768de6140c9ce769ef3f101da33f3aebcaad01e5
# reset dependencies to match branch
git checkout ./pnmp-lock.yaml

# reset builds
rm -rf ./packages/components/node_modules
rm -rf ./packages/ui/node_modules
git checkout packages/server/package.json

# change flowise logos
rm ./packages/ui/src/assets/images/flowise_dark.svg
rm ./packages/ui/src/assets/images/flowise_white.svg
cp ../assets/images/flowise_dark.svg ./packages/ui/src/assets/images/flowise_dark.svg
cp ../assets/images/flowise_white.svg ./packages/ui/src/assets/images/flowise_white.svg

# change flowise stylesheet
rm ./packages/ui/src/assets/scss/_themes-vars.module.scss
cp ../assets/scss/_themes-vars.module.scss ./packages/ui/src/assets/scss/_themes-vars.module.scss

# build flowise
# pnpm install 
# pnpm build
rm -rf ./node_modules
mv pnpm-lock.yaml ./pnpm-lock.yaml.bak
cd ../

find ./Flowise -name "package.json" -type f -exec sed -i '' 's|workspace:\^|*|g' {} \;
npm install --force
npm run build
npm install canvas
node node_modules/couchbase/scripts/install.js
# fix cpu features not missing buildcheck.gypi (may be fixed, but attempts nonetheless)
# node node_modules/cpu-features@0.0.10/node_modules/cpu-features/buildcheck.js > node_modules/cpu-features@0.0.10/node_modules/cpu-features/buildcheck.gypi

#unclear but electron doesn't work off the bat, so wanting to just run this for now.
node node_modules/electron/install.js

mv ./Flowise/pnpm-lock.yaml.bak ./pnpm-lock.yaml

npm run build:electron
npm run electron dev