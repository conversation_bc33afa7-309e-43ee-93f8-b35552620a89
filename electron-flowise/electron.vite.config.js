import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import dotenv from 'dotenv'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'

export default defineConfig(async ({ mode }) => {
    dotenv.config()

    return {
        main: {
            plugins: [externalizeDepsPlugin()],
        },
        preload: {
            plugins: [externalizeDepsPlugin()],
        },
        renderer: {
            plugins: [react()],
            resolve: {
                alias: {
                    '@': resolve('src/renderer/src'),
                    '@renderer': resolve('src/renderer/src')
                }
            },
        }
    }
})
