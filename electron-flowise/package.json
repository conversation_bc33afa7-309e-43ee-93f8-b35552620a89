{"name": "souls-flowise-monorepo", "version": "1.0.0", "private": true, "workspaces": ["Flowise/packages/ui", "Flowise/packages/components", "Flowise/packages/server"], "scripts": {"build": "turbo run build", "build-force": "npm run clean && turbo run build --force", "clean": "npm run --filter \"./Flowise/packages/**\" clean", "build:electron": "electron-vite build", "electron": "npm run build:electron && electron out/main/index.js", "dist:mac": "export NODE_OPTIONS='--max-old-space-size=24576' && electron-builder --mac", "dist:win": "export NODE_OPTIONS='--max-old-space-size=24576' && electron-builder --win", "package": "npm run build && electron-builder", "lint": "eslint src/**/*.ts", "lint-fix": "eslint src/**/*.ts --fix", "package:mac": "export NODE_OPTIONS='--max-old-space-size=24576' && npm run build:electron && electron-builder --mac", "package:win": "export NODE_OPTIONS='--max-old-space-size=24576' && npm run build:electron && electron-builder --win", "package:linux": "export NODE_OPTIONS='--max-old-space-size=24576' && npm run build:electron && electron-builder --linux"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": "eslint --fix"}, "devDependencies": {"@babel/preset-env": "^7.19.4", "@babel/preset-typescript": "7.18.6", "@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^24.0.10", "@typescript-eslint/typescript-estree": "^7.13.1", "electron": "^37.2.0", "electron-builder": "^26.0.12", "electron-vite": "^1.0.15", "eslint": "^8.24.0", "eslint-config-prettier": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^4.1.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "husky": "^8.0.1", "kill-port": "^2.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "turbo": "1.10.16", "typescript": "5.8.3", "vite": "4.2.0", "@vitejs/plugin-react": "^4.2.0", "react": "18.3.1", "react-dom": "18.3.1", "@mui/system": "5.15.0", "@mui/material": "5.15.0", "@mui/icons-material": "5.0.3", "react-router": "~6.3.0", "react-router-dom": "~6.3.0"}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20"}, "resolutions": {"@google/generative-ai": "^0.24.0", "@grpc/grpc-js": "^1.10.10", "@langchain/core": "0.3.61", "@qdrant/openapi-typescript-fetch": "1.2.6", "openai": "4.96.0", "protobufjs": "7.4.0", "@mui/icons-material": "5.0.3", "react-dom": "18.3.1", "@types/react": "^17.0.0"}, "overrides": {"@google/generative-ai": "^0.24.0", "@grpc/grpc-js": "^1.10.10", "@langchain/core": "0.3.37", "@qdrant/openapi-typescript-fetch": "1.2.6", "openai": "4.96.0", "protobufjs": "7.4.0", "mongodb": "6.3.0", "officeparser": "5.1.1", "canvas": "^3.1.2", "@material-ui/core": "5.0.0-beta.0", "@material-ui/icons": "5.0.0-beta.0", "react-dom": "18.3.1", "@mui/system": "5.15.0", "@mui/material": "5.15.0", "@mui/icons-material": "5.0.3", "axios": "1.7.9", "body-parser": "2.0.2", "braces": "3.0.3", "cross-spawn": "7.0.6", "glob-parent": "6.0.2", "http-proxy-middleware": "3.0.3", "json5": "2.2.3", "nth-check": "2.1.1", "path-to-regexp": "0.1.12", "prismjs": "1.29.0", "semver": "7.7.1", "set-value": "4.1.0", "unset-value": "2.0.1", "webpack-dev-middleware": "7.4.2", "@langchain/community": "0.3.29", "zod": "3.23.8", "typescript": "5.8.3", "@types/react": "^17.0.0"}, "eslintIgnore": ["**/dist", "**/node_modules", "**/build", "**/package-lock.json"], "prettier": {"printWidth": 140, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "none", "tabWidth": 4, "semi": false, "endOfLine": "auto"}, "babel": {"presets": ["@babel/preset-typescript", ["@babel/preset-env", {"targets": {"node": "current"}}]]}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@ibm-cloud/watsonx-ai": "^1.6.4", "@uiw/react-codemirror": "^4.21.21", "canvas": "^3.1.2", "electron-log": "^5.4.1", "react-redux": "^8.0.5", "react": "^18.2.1", "react-dom": "18.3.1", "@mui/system": "5.15.0", "@mui/material": "5.15.0", "@mui/icons-material": "5.15.0", "react-router": "~6.3.0", "react-router-dom": "~6.3.0", "flowise": "*", "flowise-components": "*", "flowise-ui": "*"}, "build": {"extends": null, "asar": true, "appId": "com.souls.ai", "productName": "SOULS", "files": ["!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "node_modules/**/*", "out/**/*", "Flowise/packages/server/bin/**/*", "Flowise/packages/server/dist/**/*", "Flowise/packages/server/marketplaces/**/*", "Flowise/packages/server/node_modules/**/*", "Flowise/packages/server/package.json", "Flowise/packages/ui/build/**/*", "Flowise/packages/ui/package.json", "Flowise/packages/ui/node_modules/**/*", "Flowise/packages/components/dist/**/*", "Flowise/packages/components/package.json", "Flowise/packages/components/models.json", "Flowise/packages/components/node_modules/**/*", "package.json"], "extraResources": [{"from": "Flowise/packages/server/node_modules", "to": "app/Flowise/packages/server/node_modules"}, {"from": "Flowise/packages/server/dist", "to": "app/Flowise/packages/server/dist"}, {"from": "Flowise/packages/server/marketplaces", "to": "app/Flowise/packages/server/marketplaces"}, {"from": "Flowise/packages/server/bin", "to": "app/Flowise/packages/server/bin"}, {"from": "Flowise/packages/server/package.json", "to": "app/Flowise/packages/server/package.json"}, {"from": "Flowise/packages/ui/node_modules", "to": "app/Flowise/packages/ui/node_modules"}, {"from": "Flowise/packages/ui/build", "to": "app/Flowise/packages/ui/build"}, {"from": "Flowise/packages/ui/package.json", "to": "app/Flowise/packages/ui/package.json"}, {"from": "Flowise/packages/components/node_modules", "to": "app/Flowise/packages/components/node_modules"}, {"from": "Flowise/packages/components/dist", "to": "app/Flowise/packages/components/dist"}, {"from": "Flowise/packages/components/package.json", "to": "app/Flowise/packages/components/package.json"}, {"from": "Flowise/packages/components/models.json", "to": "app/Flowise/packages/components/models.json"}, {"from": "node_modules", "to": "app/node_modules"}], "directories": {"buildResources": "./assets/icons"}, "mac": {"target": "dmg", "icon": "./assets/icons/app-icon.png"}, "win": {"target": "nsis", "icon": "./assets/icons/app-icon.png"}, "linux": {"target": "AppImage", "icon": "./assets/icons/app-icon.png"}}, "main": "out/main/index.js", "gypfile": true, "keywords": [], "author": "", "license": "ISC", "description": ""}