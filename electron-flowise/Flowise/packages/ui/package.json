{"name": "flowise-ui", "version": "3.0.3", "license": "SEE LICENSE IN LICENSE.md", "homepage": "https://flowiseai.com", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {"@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.2.5", "@codemirror/view": "^6.26.3", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@lezer/highlight": "^1.2.1", "@microsoft/fetch-event-source": "^2.0.1", "@mui/base": "5.0.0-beta.40", "@mui/icons-material": "5.15.0", "@mui/lab": "5.0.0-alpha.156", "@mui/material": "5.15.0", "@mui/system": "^6.4.3", "@mui/x-data-grid": "6.8.0", "@mui/x-tree-view": "^7.25.0", "@reduxjs/toolkit": "^2.2.7", "@tabler/icons-react": "^3.30.0", "@tiptap/extension-mention": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@uiw/codemirror-theme-sublime": "^4.21.21", "@uiw/codemirror-theme-vscode": "^4.21.21", "@uiw/react-codemirror": "^4.21.21", "axios": "1.7.9", "clsx": "^1.1.1", "dotenv": "^16.0.0", "flowise-embed": "latest", "flowise-embed-react": "latest", "flowise-react-json-view": "*", "formik": "^2.2.6", "framer-motion": "^4.1.13", "history": "^5.0.0", "html-react-parser": "^3.0.4", "lodash": "^4.17.21", "moment": "^2.29.3", "notistack": "^2.0.4", "prop-types": "^15.7.2", "react": "^18.2.0", "react-code-blocks": "^0.1.6", "react-color": "^2.19.3", "react-datepicker": "^4.21.0", "react-device-detect": "^1.17.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.6", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.5", "react-rewards": "^2.1.0", "react-router": "~6.3.0", "react-router-dom": "~6.3.0", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.5.6", "recharts": "^2.12.6", "redux": "^4.0.5", "rehype-mathjax": "^4.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "showdown": "^2.1.0", "tippy.js": "^6.3.7", "uuid": "^9.0.1", "yup": "^0.32.9"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "clean": "<PERSON><PERSON><PERSON> build", "nuke": "rimraf build node_modules .turbo"}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/eslint-parser": "^7.15.8", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^5.11.10", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^12.8.3", "@vitejs/plugin-react": "^4.2.0", "pretty-quick": "^3.1.3", "react-scripts": "^5.0.1", "rimraf": "^5.0.5", "sass": "^1.42.1", "typescript": "^5.4.5", "vite": "^5.0.2", "vite-plugin-pwa": "^0.17.0", "vite-plugin-react-js-support": "^1.0.7"}}