const { app, <PERSON>rowserWindow } = require('electron')
const path = require('path')
const { fork, spawn } = require('child_process')
const fs = require('fs')
const url = require('url')
const http = require('http')
const log = require('electron-log')
console.log = log.log
console.info = log.info
console.error = log.error

// Store references
let mainWindow
let serverProcess
let serverCheckInterval

// Determine if we're in development or production
const isDev = !app.isPackaged

// Get the correct path for resources in production
function getResourcePath(relativePath) {
    if ('resourcesPath' in process) {
        return isDev ? path.join(__dirname, '../', relativePath) : path.join(process.resourcesPath, 'app', relativePath)
    }
    return path.join(__dirname, '../', relativePath)
}

// Check if localhost:3000 is available
function checkServerAvailability() {
    return new Promise((resolve) => {
        const req = http.get('http://localhost:3000', (res) => {
            resolve(true)
        })

        req.on('error', () => {
            resolve(false)
        })

        req.setTimeout(1000, () => {
            req.destroy()
            resolve(false)
        })
    })
}

// Start the server
function startServer() {
    console.log('Starting server...')
    const serverBinPath = isDev
        ? path.join(__dirname, '../../Flowise/packages/server/bin/run')
        : getResourcePath('Flowise/packages/server/bin/run')

    const serverProcess = fork(serverBinPath, ['start'], {
        env: { ...process.env, ELECTRON_RUN: 'true' },
        stdio: 'pipe'
    })

    serverProcess.stdout.on('data', (data) => {
        // eslint-disable-next-line
        console.log(`Server: ${data}`)
    })

    serverProcess.stderr.on('data', (data) => {
        // eslint-disable-next-line
        console.error(`Server error: ${data}`)
    })

    serverProcess.on('exit', (code, signal) => {
        // eslint-disable-next-line
        console.info(`Server exited with code: ${code}, signal: ${signal}`)

        // If the main window is still showing localhost:3000, reload the renderer
        if (mainWindow && mainWindow.webContents.getURL().includes('localhost:3000')) {
            console.log('Server exited while showing localhost:3000, reloading renderer')
            const rendererPath = path.join(__dirname, '../renderer/index.html')
            mainWindow.loadFile(rendererPath)
        }
    })

    serverProcess.on('close', (code) => {
        // eslint-disable-next-line
        console.log(`Server process closed with code ${code}`)
    })

    console.log(`Server started pid: ${serverProcess.pid}`)

    return serverProcess
}

// Check if server process is running
function isServerProcessRunning() {
    return serverProcess && serverProcess.pid && !serverProcess.killed && serverProcess.exitCode === null
}

// Start checking for server availability
async function startServerCheck() {
    console.log('Starting server availability check...')

    serverCheckInterval = setInterval(async () => {
        // Check if server process is still running
        const processRunning = isServerProcessRunning()
        console.log(`Server process running: ${processRunning}`)

        if (processRunning) {
            const isAvailable = await checkServerAvailability()
            console.log(`Server availability check: ${isAvailable}`)

            if (isAvailable && mainWindow) {
                console.log('Server is available, switching to localhost:3000')
                mainWindow.loadURL('http://localhost:3000')

                // Clear the interval once we've successfully loaded the server
                if (serverCheckInterval) {
                    clearInterval(serverCheckInterval)
                    serverCheckInterval = null
                }
            }
        } else {
            console.log('Server process not running, waiting longer...')
        }
    }, 2000) // Check every 2 seconds
}

// Create the main window
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1280,
        height: 900,
        title: "SOULS - AI You Own. Don't Sell Your Soul.",
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: true,
            preload: path.join(__dirname, '../preload/index.js')
        }
    })

    // Initially load the renderer index.html from out folder
    const rendererPath = path.join(__dirname, '../renderer/index.html')

    mainWindow.loadFile(rendererPath)
    console.log('Loading initial renderer from:', rendererPath)

    // Start checking for server availability
    // startServerCheck()

    if (isDev) {
        mainWindow.webContents.openDevTools()
    }
}

// App initialization
app.whenReady().then(() => {
    // Start the server first
    serverProcess = startServer()

    // Then create the window
    createWindow()
})

// Quit when all windows are closed
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit()
    }
})

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow()
    }
})

// Clean up on app quit
app.on('before-quit', () => {
    // Clear server check interval
    if (serverCheckInterval) {
        clearInterval(serverCheckInterval)
        serverCheckInterval = null
    }

    // Kill server process
    if (serverProcess && serverProcess.pid) {
        try {
            // Check if process is still running
            const isRunning = !serverProcess.killed && serverProcess.exitCode === null

            if (isRunning) {
                console.log(`Attempting to kill server process ${serverProcess.pid}`)

                if (process.platform === 'win32') {
                    spawn('taskkill', ['/pid', serverProcess.pid.toString(), '/f', '/t'])
                    console.log('Server process kill command sent (Windows)')
                } else {
                    const killed = process.kill(serverProcess.pid, 'SIGTERM')
                    console.log(`Server process ${killed ? 'killed' : 'could not be killed'}`)
                }
            } else {
                console.log('Server process already terminated')
            }
        } catch (error) {
            console.error('Error killing server process:', error.message)
        }
    } else {
        console.log('No server process to kill')
    }
})
